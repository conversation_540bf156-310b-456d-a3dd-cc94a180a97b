{"name": "@roo-code/ipc", "description": "IPC server and client for remote Roo Code access.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "clean": "rimraf .turbo"}, "dependencies": {"@roo-code/types": "workspace:^", "node-ipc": "^12.0.0"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/node": "20.x", "@types/node-ipc": "^9.2.3", "vitest": "^3.2.3"}}