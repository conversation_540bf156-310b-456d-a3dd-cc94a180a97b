<complete_extraction_examples>
  <overview>
    Examples of both documentation extraction and verification workflows demonstrating flexible discovery methods and comprehensive UI/UX analysis.
  </overview>

  <example name="authentication_system_extraction">
    <scenario>
      Extract comprehensive documentation for a JWT-based authentication system, including technical implementation, UI/UX elements, and user workflows.
    </scenario>

    <workflow>
      <step number="1">
        <description>Initialize and discover feature using flexible methods</description>
        <tool_use><![CDATA[
<!-- Method 1: Directory exploration -->
<list_files>
<path>src</path>
<recursive>false</recursive>
</list_files>
        ]]></tool_use>
        <analysis>Look for auth-related directories like auth/, authentication/, or security/</analysis>
        <tool_use><![CDATA[
<list_files>
<path>src/auth</path>
<recursive>true</recursive>
</list_files>
        ]]></tool_use>
        <expected_findings>
          - Auth controllers, services, middleware, models, and routes
          - Login components and forms
          - Session management UI
        </expected_findings>
      </step>

      <step number="2">
        <description>Analyze code structure and architecture</description>
        <tool_use><![CDATA[
<list_code_definition_names>
<path>src/auth</path>
</list_code_definition_names>
        ]]></tool_use>
        <analysis>
          - Identify main classes/functions
          - Map authentication flow
          - Find token generation/validation logic
          - Locate UI components
        </analysis>
      </step>

      <step number="3">
        <description>Read core implementation files</description>
        <tool_use><![CDATA[
<read_file>
<args>
  <file>
    <path>src/auth/auth.controller.ts</path>
  </file>
  <file>
    <path>src/auth/auth.service.ts</path>
  </file>
  <file>
    <path>src/auth/jwt.strategy.ts</path>
  </file>
  <file>
    <path>src/auth/auth.guard.ts</path>
  </file>
  <file>
    <path>src/models/user.model.ts</path>
  </file>
</args>
</read_file>
        ]]></tool_use>
      </step>

      <step number="4">
        <description>UI/UX Analysis - Discover UI components</description>
        <tool_use><![CDATA[
<!-- Method 2: Pattern search for UI components -->
<search_files>
<path>src/components</path>
<regex>(Login|Auth|Session|Password).*\.(tsx?|jsx?)</regex>
<file_pattern>*.tsx</file_pattern>
</search_files>
        ]]></tool_use>
        <tool_use><![CDATA[
<read_file>
<args>
  <file>
    <path>src/components/LoginForm.tsx</path>
  </file>
  <file>
    <path>src/components/SessionManager.tsx</path>
  </file>
  <file>
    <path>src/components/PasswordReset.tsx</path>
  </file>
</args>
</read_file>
        ]]></tool_use>
      </step>

      <step number="5">
        <description>UI/UX Analysis - Map user interactions</description>
        <tool_use><![CDATA[
<search_files>
<path>src/components</path>
<regex>onClick|onSubmit|onChange|handleSubmit|validate</regex>
</search_files>
        ]]></tool_use>
        <analysis>
          - Form validation patterns
          - User feedback mechanisms
          - Error handling UI
          - Loading states
        </analysis>
      </step>

      <step number="6">
        <description>UI/UX Analysis - Visual patterns and accessibility</description>
        <tool_use><![CDATA[
<search_files>
<path>src</path>
<regex>className=|style=|theme\.|aria-|role=</regex>
<file_pattern>*.tsx</file_pattern>
</search_files>
        ]]></tool_use>
        <tool_use><![CDATA[
<search_files>
<path>src/styles</path>
<regex>\.login|\.auth|\.session</regex>
<file_pattern>*.css</file_pattern>
</search_files>
        ]]></tool_use>
      </step>

      <step number="7">
        <description>Extract API endpoints and configuration</description>
        <tool_use><![CDATA[
<search_files>
<path>src/auth</path>
<regex>@(Post|Get)\(['"]\/auth\/[^'"]+['"]|router\.(post|get)\(['"]\/auth\/[^'"]+['"]</regex>
</search_files>
        ]]></tool_use>
        <tool_use><![CDATA[
<search_files>
<path>src</path>
<regex>JWT_SECRET|JWT_EXPIRATION|AUTH_.*|process\.env\.\w*AUTH\w*</regex>
</search_files>
        ]]></tool_use>
      </step>

      <step number="8">
        <description>Optional: Use semantic search for deeper insights</description>
        <tool_use><![CDATA[
<!-- Only if pattern searches miss important context -->
<codebase_search>
<query>authentication error handling user feedback messages</query>
</codebase_search>
        ]]></tool_use>
        <note>Used when specific patterns don't capture all relevant error handling</note>
      </step>

      <step number="9">
        <description>Create comprehensive extraction report</description>
        <tool_use><![CDATA[
<write_to_file>
<path>EXTRACTION-authentication-system.md</path>
<content>
# Authentication System - Feature Extraction Report

## Executive Summary
The authentication system is a JWT-based security implementation that handles user registration, login, session management, and access control. It provides a comprehensive user experience with visual feedback, accessibility features, and robust error handling.

## UI/UX Analysis

### User Interface Components

#### 1. Login Page (`src/components/LoginForm.tsx`)
**Visual Layout:**
- Centered card design with shadow (400px width)
- Company logo at top
- Form fields with floating labels
- Primary blue theme (#1976d2)

**Interactive Elements:**
- Email input field
  - Real-time validation (regex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/)
  - Error state: Red border and helper text
  - Success state: Green checkmark icon
- Password field
  - Show/hide toggle button (eye icon)
  - Minimum 8 characters validation
  - Caps lock warning indicator
- "Remember me" checkbox with 30-day persistence
- "Forgot password?" link (underlined on hover)
- Submit button
  - Disabled state: Gray background until valid input
  - Loading state: Spinner replaces text
  - Success state: Checkmark animation

**User Feedback:**
- Loading overlay with spinner during authentication
- Error messages appear with slide-down animation
- Success toast notification (3s duration)
- Form shake animation on error

#### 2. Registration Form (`src/components/RegisterForm.tsx`)
**Multi-Step Design:**
- Progress bar showing 3 steps
- Smooth slide transitions between steps
- Back/Next navigation buttons

**Step 1 - Account Info:**
- Email field with async availability check
- Password field with strength meter (5 levels)
- Password confirmation with match validation

**Step 2 - Personal Info:**
- First/Last name fields
- Optional phone with format mask
- Country dropdown with flag icons

**Step 3 - Terms & Submit:**
- Terms of service scrollable text
- Privacy policy link (opens modal)
- Checkbox required for submission
- Review summary before final submit

**Visual Feedback:**
- Field validation on blur
- Progress saved in localStorage
- Success confetti animation
- Auto-redirect countdown (5s)

#### 3. Session Management (`src/components/SessionManager.tsx`)
**Device List UI:**
- Card-based layout for each session
- Device icons (FontAwesome)
  - fa-mobile for mobile
  - fa-desktop for desktop
  - fa-tablet for tablet
- Information displayed:
  - Device name and browser
  - IP address (partially masked)
  - Last active (relative time)
  - Location (city, country)

**Interactive Features:**
- Current device highlighted with blue border
- Hover state shows "Revoke" button
- Confirmation modal with device details
- Bulk selection with checkboxes
- "Revoke All" with double confirmation

### User Experience Elements

#### Visual Patterns
**Theme System:**
```css
--primary-color: #1976d2;
--error-color: #d32f2f;
--success-color: #388e3c;
--warning-color: #f57c00;
--text-primary: rgba(0, 0, 0, 0.87);
--text-secondary: rgba(0, 0, 0, 0.6);
```

**Animations:**
- Page transitions: 300ms ease-in-out
- Button hover: scale(1.02)
- Error shake: 0.5s horizontal
- Success checkmark: SVG path animation
- Loading spinner: 1s rotation

**Responsive Breakpoints:**
- Mobile: < 768px (single column)
- Tablet: 768px - 1024px
- Desktop: > 1024px

#### Accessibility Features
**Keyboard Navigation:**
- Tab order follows visual flow
- Enter key submits forms
- Escape closes modals
- Arrow keys in dropdowns

**Screen Reader Support:**
- ARIA labels on all inputs
- Live regions for errors
- Role attributes for custom components
- Descriptive button text

**Visual Accessibility:**
- 4.5:1 contrast ratio minimum
- Focus indicators (2px outline)
- Error icons for colorblind users
- Scalable fonts (rem units)

### User Workflows

#### 1. First-Time Registration
```
Start → Landing Page → "Get Started" CTA
  ↓
Registration Form (Step 1)
  → Email validation (async)
  → Password strength check
  → Real-time feedback
  ↓
Personal Info (Step 2)
  → Optional fields clearly marked
  → Format validation
  ↓
Terms Agreement (Step 3)
  → Must scroll to enable checkbox
  → Review summary
  ↓
Submit → Loading → Success
  → Confetti animation
  → Welcome email sent
  → Auto-redirect (5s)
  ↓
Dashboard (First-time tour)
```

#### 2. Returning User Login
```
Start → Login Page
  ↓
Enter Credentials
  → Email autocomplete
  → Password manager integration
  → "Remember me" option
  ↓
Submit → Loading (avg 1.2s)
  ↓
Success → Dashboard
  OR
Error → Inline feedback
  → Retry with guidance
  → "Forgot password?" option
```

#### 3. Password Reset Flow
```
Login Page → "Forgot password?"
  ↓
Modal Dialog
  → Email input
  → Captcha (if multiple attempts)
  ↓
Submit → "Check email" message
  ↓
Email Received (< 1 min)
  → Secure link (1hr expiry)
  ↓
Reset Page
  → New password requirements shown
  → Strength meter
  → Confirmation field
  ↓
Submit → Success → Login redirect
```

## Technical Details

### Core Components
1. **AuthController** (`src/auth/auth.controller.ts`)
   - REST endpoints with validation decorators
   - Rate limiting middleware
   - CORS configuration

2. **AuthService** (`src/auth/auth.service.ts`)
   - JWT token generation/validation
   - Bcrypt password hashing
   - Session management logic

3. **Security Implementation**
   - JWT RS256 algorithm
   - Refresh token rotation
   - CSRF double-submit cookies
   - XSS protection headers

### API Endpoints
| Method | Endpoint | Description | Rate Limit |
|--------|----------|-------------|------------|
| POST | /auth/register | New user registration | 3/hour |
| POST | /auth/login | User authentication | 5/min |
| POST | /auth/refresh | Token refresh | 10/min |
| POST | /auth/logout | Session termination | None |
| GET | /auth/profile | Current user data | None |
| POST | /auth/reset-password | Password reset | 3/hour |

### Configuration
```env
# Required
JWT_SECRET=minimum-32-character-secret
DATABASE_URL=postgresql://...

# Optional with defaults
JWT_EXPIRATION=15m
REFRESH_TOKEN_EXPIRATION=7d
BCRYPT_ROUNDS=10
SESSION_MAX_AGE=30d
MAX_SESSIONS_PER_USER=5
```

## Non-Technical Information

### Business Rules
1. **Account Creation**
   - Unique email required
   - Password: 8+ chars, mixed case, number, special
   - Email verification within 24 hours
   - Terms acceptance mandatory

2. **Session Management**
   - Max 5 concurrent sessions
   - Idle timeout: 30 minutes
   - Absolute timeout: 7 days
   - Device trust for 30 days

3. **Security Policies**
   - Account lockout: 5 failed attempts (15 min)
   - Password history: Last 3 not reusable
   - 2FA optional but recommended
   - Suspicious login notifications

### Common User Scenarios

#### Mobile Experience
- Touch-optimized buttons (44px min)
- Biometric login (Face ID/Touch ID)
- Simplified navigation menu
- Offline detection with retry
- Push notification for new sessions

#### Error Recovery
- Network timeout: Auto-retry with backoff
- Session expired: Smooth re-login flow
- Form errors: Contextual help text
- Server errors: Friendly messages with support link

### Performance Metrics
- Login response: 200ms (p50), 500ms (p95)
- Page load: 1.2s (3G), 400ms (4G)
- Token validation: < 10ms
- Session check: < 50ms

## Documentation Recommendations

### Critical Areas for User Documentation
1. **Getting Started Guide**
   - Screenshots of each registration step
   - Common email provider settings
   - Password manager setup

2. **Troubleshooting Section**
   - "Why can't I log in?" flowchart
   - Browser compatibility matrix
   - Cookie/JavaScript requirements

3. **Security Best Practices**
   - How to spot phishing attempts
   - Importance of unique passwords
   - When to revoke sessions

### Developer Integration Guide
1. **API Authentication**
   - Bearer token format
   - Refresh token flow diagram
   - Error response examples

2. **SDK Examples**
   - JavaScript/TypeScript
   - Python
   - Mobile (iOS/Android)

## Integration Points
- Email service for password reset and notifications
- Session storage (Redis optional, in-memory default)
- Rate limiting middleware
- CORS configuration for cross-origin requests
- Logging service for audit trails

## Summary for Documentation Team
This extraction report provides comprehensive details about the authentication system's current implementation. The system offers a complete user experience with visual feedback, accessibility features, and robust security measures. Key areas for user documentation include the multi-step registration process, session management features, and security policies. The technical implementation uses industry-standard JWT tokens with proper security measures.
</content>
<line_count>354</line_count>
</write_to_file>
        ]]></tool_use>
      </step>
    </workflow>

    <key_takeaways>
      <takeaway>Use flexible discovery methods - directory exploration and pattern search are primary tools</takeaway>
      <takeaway>Comprehensive UI/UX analysis is essential for complete documentation</takeaway>
      <takeaway>Document visual elements, interactions, and user feedback mechanisms</takeaway>
      <takeaway>Include accessibility and responsive design considerations</takeaway>
      <takeaway>Semantic search (codebase_search) is optional for deeper insights when needed</takeaway>
      <takeaway>Organize findings to support documentation team's work</takeaway>
    </key_takeaways>
  </example>

  <example name="documentation_verification_example">
    <scenario>
      Verify user-provided documentation about authentication UI/UX against the actual implementation.
    </scenario>

    <workflow>
      <step number="1">
        <description>User provides documentation to verify</description>
        <user_input>
          "Please verify our login documentation:
          
          'The login form provides a seamless experience with instant validation. Users see a green checkmark when their email is valid. The password field includes a strength indicator. After 3 failed attempts, users must wait 5 minutes before trying again.'"
        </user_input>
      </step>

      <step number="2">
        <description>Analyze the documentation claims</description>
        <analysis>
          Claims to verify:
          - Instant email validation with green checkmark
          - Password strength indicator on login
          - 3 failed attempts triggers 5-minute wait
        </analysis>
      </step>

      <step number="3">
        <description>Find login UI components using directory exploration</description>
        <tool_use><![CDATA[
<list_files>
<path>src/components</path>
<recursive>false</recursive>
</list_files>
        ]]></tool_use>
        <tool_use><![CDATA[
<search_files>
<path>src/components</path>
<regex>Login|SignIn|Auth.*Form</regex>
<file_pattern>*.tsx</file_pattern>
</search_files>
        ]]></tool_use>
      </step>

      <step number="4">
        <description>Examine login form implementation</description>
        <tool_use><![CDATA[
<read_file>
<args>
  <file>
    <path>src/components/LoginForm.tsx</path>
  </file>
  <file>
    <path>src/components/LoginForm.css</path>
  </file>
</args>
</read_file>
        ]]></tool_use>
        <findings>
          - Email validation exists but shows red X, not green checkmark
          - No password strength indicator on login form (only on registration)
          - Account lockout is after 5 attempts, not 3
          - Lockout duration is 15 minutes, not 5
        </findings>
      </step>

      <step number="5">
        <description>Verify validation behavior</description>
        <tool_use><![CDATA[
<search_files>
<path>src/components</path>
<regex>validateEmail|emailValid|checkmark|strength.*indicator</regex>
</search_files>
        ]]></tool_use>
        <tool_use><![CDATA[
<search_files>
<path>src/auth</path>
<regex>failed.*attempts|lockout|rate.*limit</regex>
</search_files>
        ]]></tool_use>
      </step>

      <step number="6">
        <description>Create verification report</description>
        <tool_use><![CDATA[
<write_to_file>
<path>VERIFICATION-login-documentation.md</path>
<content>
# Login Documentation - Verification Report

## Overall Assessment: Needs Significant Updates

## Summary
The login documentation contains several inaccuracies about the UI/UX implementation that could confuse users and lead to poor user experience.

## Critical Inaccuracies

### 1. Email Validation Feedback
**Documentation States**: "Users see a green checkmark when their email is valid"
**Actual Implementation**: Red X icon for invalid emails, no positive feedback
- Location: `src/components/LoginForm.tsx:45-52`
- Current behavior: Only shows error states
- Missing: Success indicator implementation
- **Impact**: Users don't get positive reinforcement for correct input

### 2. Password Strength Indicator
**Documentation States**: "The password field includes a strength indicator"
**Actual Implementation**: No strength indicator on login form
- Location: Checked `src/components/LoginForm.tsx`
- Strength indicator only exists in `RegisterForm.tsx`
- Login form has basic password field only
- **Impact**: Sets incorrect user expectations

### 3. Failed Login Attempts
**Documentation States**: "After 3 failed attempts, users must wait 5 minutes"
**Actual Implementation**: 5 attempts, 15-minute lockout
- Location: `src/auth/services/auth.service.ts:234`
- Code: `MAX_LOGIN_ATTEMPTS = 5`
- Code: `LOCKOUT_DURATION = 15 * 60 * 1000 // 15 minutes`
- **Impact**: Users may be confused about actual security policy

## UI/UX Elements Found But Not Documented

### 1. Loading States
- Spinner overlay during authentication
- Button disabled state during submission
- These improve perceived performance

### 2. Remember Me Feature
- 30-day persistent session option
- Stored in secure cookie
- Not mentioned in documentation

### 3. Caps Lock Warning
- Visual indicator when caps lock is on
- Helps prevent login failures
- Important accessibility feature

## Recommendations

### Immediate Corrections Needed
1. **Email Validation**:
   ```
   CORRECT: "The email field shows a red error icon for invalid formats. Valid emails are accepted without visual confirmation."
   ```

2. **Password Features**:
   ```
   CORRECT: "The login form has a standard password field with show/hide toggle. Password strength indicators are only shown during registration."
   ```

3. **Security Policy**:
   ```
   CORRECT: "After 5 failed login attempts, accounts are temporarily locked for 15 minutes to prevent brute force attacks."
   ```

### Suggested Additions
1. Document the loading states and visual feedback
2. Explain the "Remember Me" functionality
3. Mention the Caps Lock warning feature
4. Add screenshots of actual UI states

## Technical References
- Login Form Component: `src/components/LoginForm.tsx`
- Styles: `src/components/LoginForm.css`
- Auth Service: `src/auth/services/auth.service.ts`
- Rate Limiting: `src/middleware/rateLimiter.ts`

## Visual Discrepancies
- Documentation implies more visual feedback than implemented
- Color scheme in docs doesn't match actual theme
- Missing documentation for error message styling

## Conclusion
The documentation needs updates to accurately reflect the current login experience. The actual implementation is more security-focused (5 attempts, 15-min lockout) but less visually rich (no success indicators) than documented.
</content>
<line_count>95</line_count>
</write_to_file>
        ]]></tool_use>
      </step>
    </workflow>

    <key_takeaways>
      <takeaway>Always verify UI/UX claims against actual component implementation</takeaway>
      <takeaway>Check both visual elements and behavioral logic</takeaway>
      <takeaway>Look for undocumented features that users might expect</takeaway>
      <takeaway>Provide specific corrections with code references</takeaway>
      <takeaway>Consider the impact of inaccuracies on user experience</takeaway>
    </key_takeaways>
  </example>

  <example name="api_endpoint_extraction">
    <scenario>
      Extract comprehensive API documentation including endpoints, request/response formats, and error handling.
    </scenario>

    <workflow>
      <step number="1">
        <description>Discover API structure using flexible methods</description>
        <tool_use><![CDATA[
<!-- Method 1: Look for common API directories -->
<list_files>
<path>src</path>
<recursive>false</recursive>
</list_files>
        ]]></tool_use>
        <tool_use><![CDATA[
<list_files>
<path>src/api</path>
<recursive>true</recursive>
</list_files>
        ]]></tool_use>
      </step>

      <step number="2">
        <description>Find all API routes using pattern search</description>
        <tool_use><![CDATA[
<search_files>
<path>src</path>
<regex>(app|router)\.(get|post|put|patch|delete|all)\s*\(\s*['"`]([^'"`]+)['"`]</regex>
</search_files>
        ]]></tool_use>
      </step>

      <step number="3">
        <description>Extract request validation schemas</description>
        <tool_use><![CDATA[
<search_files>
<path>src</path>
<regex>@(Body|Query|Param|Headers)\(|joi\.object|yup\.object|zod\.object</regex>
</search_files>
        ]]></tool_use>
      </step>

      <step number="4">
        <description>Analyze error handling and responses</description>
        <tool_use><![CDATA[
<search_files>
<path>src</path>
<regex>@ApiResponse|response\.status\(|res\.status\(|throw new.*Error</regex>
</search_files>
        ]]></tool_use>
      </step>

      <step number="5">
        <description>Optional: Semantic search for middleware and auth</description>
        <tool_use><![CDATA[
<!-- Use when pattern search might miss complex middleware chains -->
<codebase_search>
<query>API middleware authentication authorization guards</query>
</codebase_search>
        ]]></tool_use>
      </step>

      <step number="6">
        <description>Generate API extraction report</description>
        <output>
          - Complete endpoint inventory with methods and paths
          - Request/response schemas with examples
          - Authentication requirements per endpoint
          - Rate limiting and throttling rules
          - Error response formats and codes
          - API versioning strategy
        </output>
      </step>
    </workflow>
  </example>

  <example name="ui_component_library_extraction">
    <scenario>
      Document a React component library including props, styling, accessibility, and usage patterns.
    </scenario>

    <workflow>
      <step number="1">
        <description>Discover component structure</description>
        <tool_use><![CDATA[
<list_files>
<path>src/components</path>
<recursive>true</recursive>
</list_files>
        ]]></tool_use>
      </step>

      <step number="2">
        <description>Analyze component interfaces and props</description>
        <tool_use><![CDATA[
<search_files>
<path>src/components</path>
<regex>interface\s+\w+Props|type\s+\w+Props|export\s+(default\s+)?function|export\s+const</regex>
<file_pattern>*.tsx</file_pattern>
</search_files>
        ]]></tool_use>
      </step>

      <step number="3">
        <description>Extract styling and theme usage</description>
        <tool_use><![CDATA[
<search_files>
<path>src/components</path>
<regex>styled\.|makeStyles|className=|sx=|css=</regex>
</search_files>
        ]]></tool_use>
      </step>

      <step number="4">
        <description>Document accessibility features</description>
        <tool_use><![CDATA[
<search_files>
<path>src/components</path>
<regex>aria-|role=|tabIndex|alt=|htmlFor=</regex>
</search_files>
        ]]></tool_use>
      </step>

      <step number="5">
        <description>Find usage examples and stories</description>
        <tool_use><![CDATA[
<search_files>
<path>src</path>
<regex>\.stories\.|\.story\.|examples?/|demo/</regex>
<file_pattern>*.tsx</file_pattern>
</search_files>
        ]]></tool_use>
      </step>

      <step number="6">
        <description>Create component library report</description>
        <output>
          - Component hierarchy and relationships
          - Props documentation with types and defaults
          - Styling system and customization options
          - Accessibility compliance checklist
          - Interactive examples and code snippets
          - Best practices and anti-patterns
          - Browser compatibility notes
        </output>
      </step>
    </workflow>
  </example>

  <best_practices>
    <practice name="flexible_discovery">
      <description>Use the most appropriate discovery method</description>
      <guidelines>
        <guideline>Start with directory exploration for well-organized codebases</guideline>
        <guideline>Use pattern search for specific syntax or naming conventions</guideline>
        <guideline>Apply file-based search when you know exact locations</guideline>
        <guideline>Reserve semantic search for complex conceptual queries</guideline>
      </guidelines>
    </practice>

    <practice name="comprehensive_ui_ux_coverage">
      <description>Ensure complete UI/UX documentation</description>
      <checklist>
        <item>Visual design and layout</item>
        <item>Interactive elements and states</item>
        <item>User feedback mechanisms</item>
        <item>Accessibility features</item>
        <item>Responsive behavior</item>
        <item>Animation and transitions</item>
        <item>Error states and recovery</item>
        <item>Loading and progress indicators</item>
      </checklist>
    </practice>

    <practice name="verification_thoroughness">
      <description>Verify all aspects of documentation claims</description>
      <checklist>
        <item>Technical accuracy of code examples</item>
        <item>UI element descriptions match implementation</item>
        <item>User workflows reflect actual behavior</item>
        <item>Configuration values are current</item>
        <item>Error messages match code</item>
        <item>Performance claims are realistic</item>
      </checklist>
    </practice>
  </best_practices>
</complete_extraction_examples>