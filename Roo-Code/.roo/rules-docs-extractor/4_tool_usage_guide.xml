<tool_usage_guide>
  <overview>
    Guidance on using tools for documentation extraction.
  </overview>

  <discovery_approaches>
    <approach name="flexible_discovery">
      <description>Use the most appropriate tools for the situation</description>
      <principles>
        <principle>Start with what you know - file names, directory structure, or keywords</principle>
        <principle>Use multiple discovery methods to build understanding</principle>
        <principle>Adapt your approach based on the codebase structure</principle>
      </principles>
    </approach>

    <common_tools>
      <tool name="list_files">
        <purpose>Explore directory structure and find relevant files</purpose>
        <when_to_use>
          - Starting exploration of a feature area
          - Understanding project organization
          - Finding configuration or test files
        </when_to_use>
      </tool>

      <tool name="read_file">
        <purpose>Examine specific files in detail</purpose>
        <when_to_use>
          - Analyzing implementation details
          - Understanding configuration
          - Reading documentation or comments
        </when_to_use>
        <tip>Read multiple related files together for better context</tip>
      </tool>

      <tool name="search_files">
        <purpose>Find specific patterns or text</purpose>
        <when_to_use>
          - Locating API endpoints
          - Finding configuration usage
          - Tracking down error handling
          - Discovering cross-references
        </when_to_use>
      </tool>

      <tool name="list_code_definition_names">
        <purpose>Get overview of code structure</purpose>
        <when_to_use>
          - Understanding module organization
          - Identifying main components
          - Finding test coverage
        </when_to_use>
      </tool>

      <tool name="codebase_search">
        <purpose>Semantic search when available</purpose>
        <when_to_use>
          - Finding conceptually related code
          - Discovering implementations by functionality
          - When keyword search isn't sufficient
        </when_to_use>
        <note>Optional - use when semantic understanding is needed</note>
      </tool>
    </common_tools>

    <discovery_patterns>
      <pattern name="top_down">
        <description>Start from high-level structure and drill down</description>
        <steps>
          <step>List files in feature directory</step>
          <step>Identify main entry points</step>
          <step>Follow imports and dependencies</step>
          <step>Examine implementation details</step>
        </steps>
      </pattern>

      <pattern name="test_driven">
        <description>Use tests to understand expected behavior</description>
        <steps>
          <step>Find test files for the feature</step>
          <step>Read test descriptions and scenarios</step>
          <step>Trace back to implementation</step>
          <step>Verify behavior matches tests</step>
        </steps>
      </pattern>

      <pattern name="configuration_first">
        <description>Start with configuration to understand setup</description>
        <steps>
          <step>Find configuration files</step>
          <step>Identify feature flags and settings</step>
          <step>Trace usage in code</step>
          <step>Document impacts of each setting</step>
        </steps>
      </pattern>

      <pattern name="api_focused">
        <description>Map external interfaces first</description>
        <steps>
          <step>Search for route definitions</step>
          <step>Find API controllers or handlers</step>
          <step>Trace to business logic</step>
          <step>Document request/response flow</step>
        </steps>
      </pattern>
    </discovery_patterns>
  </discovery_approaches>

  <documentation_generation_tools>
    <tool name="write_to_file">
      <purpose>Create extraction or verification report files.</purpose>
      <note>Generates reports for documentation teams, not final documentation.</note>
      <file_naming>
        - For extraction: EXTRACTION-[feature-name].md
        - For verification: VERIFICATION-[feature-name].md
      </file_naming>
      <best_practices>
        <practice>Use descriptive feature name in filename.</practice>
        <practice>Include table of contents.</practice>
        <practice>Use consistent Markdown formatting.</practice>
        <practice>Include syntax-highlighted code examples.</practice>
      </best_practices>
      <example><![CDATA[
<write_to_file>
<path>EXTRACTION-authentication-system.md</path>
<content>
# Authentication System Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
...

## Overview
The authentication system provides secure user authentication using JWT tokens...
</content>
<line_count>...</line_count>
</write_to_file>
      ]]></example>
    </tool>
    
    <tool name="ask_followup_question">
      <purpose>Clarify ambiguous requirements.</purpose>
      <when_to_use>
        <scenario>Multiple features have similar names.</scenario>
        <scenario>Documentation depth is unclear.</scenario>
        <scenario>Audience priorities are undefined.</scenario>
      </when_to_use>
      <examples>
        <example><![CDATA[
<ask_followup_question>
<question>Which authentication aspects should be the focus?</question>
<follow_up>
<suggest>The complete flow (JWT, sessions, OAuth).</suggest>
<suggest>Only JWT implementation and validation.</suggest>
<suggest>Only OAuth2 integration.</suggest>
<suggest>Password reset and recovery workflows.</suggest>
</follow_up>
</ask_followup_question>
        ]]></example>
        <example><![CDATA[
<ask_followup_question>
<question>What level of technical detail is needed?</question>
<follow_up>
<suggest>High-level overview for all audiences.</suggest>
<suggest>Detailed developer implementation.</suggest>
<suggest>API reference with code examples.</suggest>
<suggest>Full coverage for all audiences.</suggest>
</follow_up>
</ask_followup_question>
        ]]></example>
      </examples>
    </tool>
  </documentation_generation_tools>

  <analysis_strategies>
    <strategy name="file_discovery">
      <description>
        Find all files related to a feature using various methods.
      </description>
      <methods>
        <method name="directory_exploration">
          <description>Start by exploring likely directories</description>
          <tool_use><![CDATA[
<list_files>
<path>src</path>
<recursive>false</recursive>
</list_files>

<!-- Then drill into feature directory -->
<list_files>
<path>src/features/[feature-name]</path>
<recursive>true</recursive>
</list_files>
          ]]></tool_use>
        </method>

        <method name="pattern_search">
          <description>Search for feature-related patterns</description>
          <tool_use><![CDATA[
<!-- Find files with feature name -->
<search_files>
<path>src</path>
<regex>feature-name|FeatureName</regex>
</search_files>

<!-- Find related tests -->
<search_files>
<path>src</path>
<regex>describe\(['"].*Feature.*['"]|test\(['"].*feature.*['"]</regex>
<file_pattern>*.test.ts</file_pattern>
</search_files>
          ]]></tool_use>
        </method>

        <method name="configuration_discovery">
          <description>Find configuration files</description>
          <tool_use><![CDATA[
<!-- Look for config files -->
<list_files>
<path>config</path>
<recursive>true</recursive>
</list_files>

<!-- Search for feature config -->
<search_files>
<path>.</path>
<regex>feature.*config|settings.*feature</regex>
<file_pattern>*.json</file_pattern>
</search_files>
          ]]></tool_use>
        </method>

        <method name="semantic_search_optional">
          <description>Use semantic search if available and helpful</description>
          <tool_use><![CDATA[
<!-- Optional: Use when concept-based search is needed -->
<codebase_search>
<query>feature implementation main logic</query>
</codebase_search>
          ]]></tool_use>
          <note>This is optional - use when other methods aren't sufficient</note>
        </method>
      </methods>
    </strategy>

    <strategy name="dependency_chain_analysis">
      <description>
        Follow import chains to map dependencies.
      </description>
      <process>
        <step>Read main file.</step>
        <step>Extract all imports.</step>
        <step>Read each imported file.</step>
        <step>Recursively analyze imports.</step>
        <step>Build dependency graph.</step>
      </process>
      <import_patterns><![CDATA[
<!-- TypeScript/JavaScript imports -->
<search_files>
<path>src/feature</path>
<regex>import\s+(?:{[^}]+}|\*\s+as\s+\w+|\w+)\s+from\s+['"]([^'"]+)['"]</regex>
</search_files>

<!-- CommonJS requires -->
<search_files>
<path>src/feature</path>
<regex>require\(['"]([^'"]+)['"]\)</regex>
</search_files>
      ]]></import_patterns>
    </strategy>

    <strategy name="api_documentation_extraction">
      <description>
        Extract API documentation from code.
      </description>
      <extraction_points>
        <point>Route definitions, request/response schemas, auth requirements, rate limiting, error responses.</point>
      </extraction_points>
      <tools_sequence>
        <sequence>
          <step>Find route files.</step>
          <step>Extract route definitions.</step>
          <step>Find controllers.</step>
          <step>Analyze request validation.</step>
          <step>Document response formats.</step>
        </sequence>
      </tools_sequence>
    </strategy>

    <strategy name="test_driven_documentation">
      <description>
        Use tests to document expected behavior.
      </description>
      <benefits>
        <benefit>Tests provide usage examples.</benefit>
        <benefit>Test descriptions explain functionality.</benefit>
        <benefit>Tests cover edge cases.</benefit>
        <benefit>Tests document expected outputs.</benefit>
      </benefits>
      <extraction_approach><![CDATA[
<!-- Find test descriptions -->
<search_files>
<path>__tests__</path>
<regex>(describe|it|test)\(['"]([^'"]+)['"]</regex>
</search_files>

<!-- Extract test scenarios -->
<read_file>
<path>__tests__/feature.test.ts</path>
</read_file>
      ]]></extraction_approach>
    </strategy>
  </analysis_strategies>

  <common_patterns>
    <pattern name="configuration_documentation">
      <search_locations>
        <location>.env.example</location>
        <location>config/*.json</location>
        <location>src/config/*</location>
        <location>README.md (configuration section)</location>
      </search_locations>
      <extraction_regex><![CDATA[
# Environment variables
process\.env\.(\w+)

# Config object access
config\.(\w+)\.(\w+)

# Default values
\w+\s*=\s*process\.env\.\w+\s*\|\|\s*['"]([^'"]+)['"]
      ]]></extraction_regex>
    </pattern>

    <pattern name="error_documentation">
      <error_patterns>
        <pattern>Custom error classes</pattern>
        <pattern>Error code constants</pattern>
        <pattern>Error message templates</pattern>
        <pattern>HTTP status codes</pattern>
      </error_patterns>
      <search_approach><![CDATA[
<search_files>
<path>src</path>
<regex>class\s+\w*Error\s+extends|new Error\(|throw new|ERROR_CODE|HTTP_STATUS</regex>
</search_files>
      ]]></search_approach>
    </pattern>

    <pattern name="security_documentation">
      <security_aspects>
        <aspect>Authentication methods</aspect>
        <aspect>Authorization rules</aspect>
        <aspect>Data encryption</aspect>
        <aspect>Input validation</aspect>
        <aspect>Rate limiting</aspect>
      </security_aspects>
      <indicators><![CDATA[
<search_files>
<path>src</path>
<regex>@Authorized|requireAuth|checkPermission|encrypt|decrypt|sanitize|validate|rateLimit</regex>
</search_files>
      ]]></indicators>
    </pattern>
  </common_patterns>

  <output_optimization>
    <guideline name="structure">
      <description>Organize output for navigation.</description>
      <structure>
        - Clear hierarchy, consistent headings, ToC with links, cross-references.
      </structure>
    </guideline>

    <guideline name="code_examples">
      <description>Include relevant code examples.</description>
      <best_practices>
        - Use syntax highlighting, show request/response, include error cases.
      </best_practices>
    </guideline>

    <guideline name="visuals">
      <description>Suggest diagrams where helpful.</description>
      <diagram_types>
        - Architecture, sequence, data flow, state machine diagrams.
      </diagram_types>
    </guideline>

    <guideline name="metadata">
      <description>Include important metadata.</description>
      <required_metadata>
        - Version compatibility, last updated, status, performance, security.
      </required_metadata>
    </guideline>
  </output_optimization>
</tool_usage_guide>