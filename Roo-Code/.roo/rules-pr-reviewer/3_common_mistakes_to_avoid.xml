<common_mistakes_to_avoid>
  - Not creating a todo list at the start to track the review workflow
  - Using MCP tools instead of GitHub CLI commands for GitHub operations
  - Forgetting to fetch headRefOid in Step 2 - this is REQUIRED for API review submission
  - Starting to review code WITHOUT first fetching existing comments and reviews
  - Failing to create a list of existing feedback before reviewing
  - Not systematically checking each existing comment against the current code
  - Repeating feedback that has already been addressed in the current code
  - Ignoring existing PR comments or failing to verify if they have already been resolved
  - Running tests or executing code during review
  - Making judgmental or harsh comments
  - Providing feedback on code outside the PR's scope
  - Overlooking unrelated changes not tied to the main issue
  - Including ANY praise or positive comments - focus only on issues
  - Using markdown headings (###, ##, #) in review comments
  - Using excessive markdown formatting when plain text would suffice
  - Submitting comments without user preview/approval
  - Forgetting to check for an associated issue for additional context
  - Missing critical security or performance issues
  - Not checking for proper i18n in UI changes
  - Failing to suggest breaking up large PRs
  - Using internal evaluation terminology in public comments
  - Not providing actionable suggestions for improvements
  - Reviewing only the diff without local context
  - Making assumptions instead of asking clarifying questions about potential intentions
  - Forgetting to link to specific lines with full GitHub URLs
  - Not presenting findings in a clear numbered list format
  - Failing to offer the task creation option for addressing suggestions
  - Creating tasks without specific context or file references
  - Choosing inappropriate modes when creating tasks for suggestions
  - Not updating the todo list after completing each step
  - Not including --repo flag when using gh commands for non-default repositories
  - Using wrong commit_id in review payload (must use headRefOid from PR info)
  - Forgetting to specify "side": "RIGHT" for comments on new code
  - Using incorrect line numbers that don't match the actual diff
  - Not escaping special characters in JSON payload properly
  - Using wrong event type (e.g., REQUEST_CHANGES when only commenting)
  - Not constructing proper file paths relative to repository root
  - Submitting empty comments array when inline comments are needed
  - Forgetting to use <<EOF syntax properly in the command
  - Not properly escaping special characters in heredoc JSON content
  - Missing the EOF delimiter at the end of the heredoc
</common_mistakes_to_avoid>