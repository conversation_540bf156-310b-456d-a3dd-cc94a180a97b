<workflow>
  <step number="1">
    <name>Initialize Review Process</name>
    <instructions>
      Create a todo list to track the PR review workflow:
      
      <update_todo_list>
      <todos>
      [ ] Fetch pull request information
      [ ] Fetch associated issue (if any)
      [ ] Fetch pull request diff
      [ ] Fetch existing PR comments and reviews
      [ ] Check out pull request locally
      [ ] Verify existing comments against current code
      [ ] Perform comprehensive review
      [ ] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
      
      This helps track progress through the review process and ensures all steps are completed.
    </instructions>
  </step>

  <step number="2">
    <name>Fetch Pull Request Information</name>
    <instructions>
      If the user provides a PR number or URL, extract the necessary information:
      - Repository owner and name
      - Pull request number
      
      Use the GitHub CLI to fetch the PR details:
      
      <execute_command>
      <command>gh pr view [PR_NUMBER] --repo [owner]/[repo] --json number,title,body,author,state,url,headRefName,baseRefName,headRefOid,mergeable,isDraft,createdAt,updatedAt</command>
      </execute_command>
      
      Parse the JSON output to understand the PR's current state and metadata.
      IMPORTANT: Save the headRefOid value as it will be needed for submitting the review via the API.
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [ ] Fetch associated issue (if any)
      [ ] Fetch pull request diff
      [ ] Fetch existing PR comments and reviews
      [ ] Check out pull request locally
      [ ] Verify existing comments against current code
      [ ] Perform comprehensive review
      [ ] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="3">
    <name>Fetch Associated Issue (If Any)</name>
    <instructions>
      Check the pull request body for a reference to a GitHub issue (e.g., "Fixes #123", "Closes #456").
      If an issue is referenced, use the GitHub CLI to fetch its details:

      <execute_command>
      <command>gh issue view [issue_number] --repo [owner]/[repo] --json number,title,body,author,state,url,createdAt,updatedAt,comments</command>
      </execute_command>

      The issue description and comments can provide valuable context for the review.
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [ ] Fetch pull request diff
      [ ] Fetch existing PR comments and reviews
      [ ] Check out pull request locally
      [ ] Verify existing comments against current code
      [ ] Perform comprehensive review
      [ ] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="4">
    <name>Fetch Pull Request Diff</name>
    <instructions>
      Get the pull request diff to understand the changes:
      
      <execute_command>
      <command>gh pr diff [PR_NUMBER] --repo [owner]/[repo]</command>
      </execute_command>
      
      This will show the complete diff of all changes in the PR.
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [x] Fetch pull request diff
      [ ] Fetch existing PR comments and reviews
      [ ] Check out pull request locally
      [ ] Verify existing comments against current code
      [ ] Perform comprehensive review
      [ ] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="5">
    <name>Fetch Existing PR Comments and Reviews</name>
    <instructions>
      IMPORTANT: Before reviewing any code, first get all existing comments and reviews to understand what feedback has already been provided:
      
      Fetch all review comments:
      <execute_command>
      <command>gh pr view [PR_NUMBER] --repo [owner]/[repo] --comments</command>
      </execute_command>
      
      Also fetch review details:
      <execute_command>
      <command>gh api repos/[owner]/[repo]/pulls/[PR_NUMBER]/reviews</command>
      </execute_command>
      
      Create a mental or written list of:
      - All issues/suggestions that have been raised
      - The specific files and line numbers mentioned
      - Whether comments appear to be resolved or still pending
      
      This information will guide your review to avoid duplicate feedback.
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [x] Fetch pull request diff
      [x] Fetch existing PR comments and reviews
      [ ] Check out pull request locally
      [ ] Verify existing comments against current code
      [ ] Perform comprehensive review
      [ ] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="6">
    <name>Check Out Pull Request Locally</name>
    <instructions>
      Use the GitHub CLI to check out the pull request locally:
      
      <execute_command>
      <command>gh pr checkout [PR_NUMBER] --repo [owner]/[repo]</command>
      </execute_command>
      
      This allows you to:
      - Navigate the actual code structure
      - Understand how changes interact with existing code
      - Get better context for your review
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [x] Fetch pull request diff
      [x] Fetch existing PR comments and reviews
      [x] Check out pull request locally
      [ ] Verify existing comments against current code
      [ ] Perform comprehensive review
      [ ] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="7">
    <name>Verify Existing Comments Against Current Code</name>
    <instructions>
      Now that you have the code checked out locally and know what comments exist:
      
      1. For each existing comment/review point:
         - Navigate to the specific file and line mentioned
         - Check if the issue has been addressed in the current code
         - Mark it as "resolved" or "still pending" in your notes
      
      2. Use read_file or codebase_search to examine the specific areas mentioned in comments:
         - If a comment says "missing error handling on line 45", check if error handling now exists
         - If a review mentioned "this function needs tests", check if tests have been added
         - If feedback was about code structure, verify if refactoring has occurred
      
      3. Keep track of:
         - Comments that have been addressed (DO NOT repeat these)
         - Comments that are still valid (you may reinforce these if critical)
         - New issues not previously mentioned (these are your main focus)
      
      This verification step is CRITICAL to avoid redundant feedback and ensures your review adds value.
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [x] Fetch pull request diff
      [x] Fetch existing PR comments and reviews
      [x] Check out pull request locally
      [x] Verify existing comments against current code
      [ ] Perform comprehensive review
      [ ] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="8">
    <name>Perform Comprehensive Review</name>
    <instructions>
      Review the pull request thoroughly:
      - Verify that the changes are directly related to the linked issue and do not include unrelated modifications.
      - Focus primarily on the changes made in the PR.
      - Prioritize code quality, code smell, structural consistency, and for UI-related changes, ensure proper internationalization (i18n) is applied.
      - Watch for signs of technical debt (e.g., overly complex logic, lack of abstraction, tight coupling, missing tests, TODOs).
      - For large PRs, alert the user and recommend breaking it up if appropriate.
      - NEVER run tests or execute code in PR Reviewer mode. The repository likely has automated testing. Your role is limited to:
        - Code review and analysis
        - Leaving review comments
        - Checking code quality and structure
        - Reviewing test coverage and quality (without execution)
      
      Document your findings in a numbered list format:
      1. Code quality issues
      2. Structural improvements
      3. Missing tests or documentation
      4. Potential bugs or edge cases
      5. Performance concerns
      6. Security considerations
      7. Internationalization (i18n) issues
      8. Technical debt indicators
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [x] Fetch pull request diff
      [x] Fetch existing PR comments and reviews
      [x] Check out pull request locally
      [x] Verify existing comments against current code
      [x] Perform comprehensive review
      [ ] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="9">
    <name>Prepare Review Comments</name>
    <instructions>
      Format your review comments following these guidelines:
      
      CRITICAL: Before adding any comment, verify it's not already addressed:
      - Cross-reference with your notes from Step 7
      - Only comment on NEW issues or UNRESOLVED existing issues
      - Never repeat feedback that has been addressed in the current code
      
      Your suggestions should:
      - Use a **friendly, curious tone** — prefer asking: "Is this intentional?" or "Could we approach this differently to improve X?"
      - Avoid assumptions or judgments; ask questions instead of declaring problems.
      - Skip ALL praise and positive comments. Focus exclusively on issues that need attention.
      - Use Markdown sparingly — only for code blocks or when absolutely necessary for clarity. Avoid markdown headings (###, ##, etc.) entirely.
      - Avoid including internal evaluation terminology (e.g., scores or internal tags) in public comments.
      
      When linking to specific lines or files, use full GitHub URLs relative to the repository, e.g.
      `https://github.com/[owner]/[repo]/blob/[branch]/[path/to/file]#L[line-number]`.
      
      Present your findings as a numbered list organized by priority:
      
      **Critical Issues (Must Fix):**
      1. [Issue description with file/line reference]
      2. [Issue description with file/line reference]
      
      **Important Suggestions (Should Consider):**
      3. [Suggestion with rationale]
      4. [Suggestion with rationale]
      
      **Minor Improvements (Nice to Have):**
      5. [Improvement suggestion]
      6. [Improvement suggestion]
      
      Include a note about which existing comments you verified as resolved (for user awareness).
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [x] Fetch pull request diff
      [x] Fetch existing PR comments and reviews
      [x] Check out pull request locally
      [x] Verify existing comments against current code
      [x] Perform comprehensive review
      [x] Prepare review comments
      [ ] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="10">
    <name>Preview Review with User</name>
    <instructions>
      Always show the user a preview of your review suggestions and comments before taking any action.
      Present your findings as a numbered list clearly for the user before submitting comments.
      
      <ask_followup_question>
      <question>I've completed my review of PR #[number]. Here's what I found:

      [If applicable: **Existing comments that have been resolved:**
      - Comment about X on file Y - now addressed
      - Suggestion about Z - implemented]

      **Review Findings:**
      
      **Critical Issues (Must Fix):**
      1. [Specific issue with file/line reference]
      2. [Specific issue with file/line reference]
      
      **Important Suggestions (Should Consider):**
      3. [Suggestion with rationale]
      4. [Suggestion with rationale]
      
      **Minor Improvements (Nice to Have):**
      5. [Improvement suggestion]
      6. [Improvement suggestion]

      Would you like me to:</question>
      <follow_up>
      <suggest>Create a comprehensive review with all comments</suggest>
      <suggest>Create individual tasks for each suggestion using new_task</suggest>
      <suggest>Let me modify the suggestions first</suggest>
      <suggest>Skip submission - just wanted the analysis</suggest>
      </follow_up>
      </ask_followup_question>
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [x] Fetch pull request diff
      [x] Fetch existing PR comments and reviews
      [x] Check out pull request locally
      [x] Verify existing comments against current code
      [x] Perform comprehensive review
      [x] Prepare review comments
      [x] Preview review with user
      [ ] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="11">
    <name>Submit Review</name>
    <instructions>
      Based on user preference, submit the review using the GitHub API to support inline comments:
      
      1. Construct the review payload with inline comments. For each comment, you need:
         - The file path (relative to repository root)
         - The line number where the comment should appear
         - The comment body text
         - The side ("RIGHT" for new code, "LEFT" for old code)
      
      2. Submit the review using the GitHub API with heredoc syntax:
      <execute_command>
      <command>gh api -X POST repos/[owner]/[repo]/pulls/[PR_NUMBER]/reviews --input - <<EOF
{
  "commit_id": "[headRefOid from Step 2]",
  "body": "Thank you for your contribution! I've reviewed the changes and [found issues that need attention / have some suggestions for improvement].",
  "event": "[COMMENT|REQUEST_CHANGES|APPROVE]",
  "comments": [
    {
      "path": "[file/path/to/code.ts]",
      "body": "[Your comment text here - use friendly, curious tone]",
      "line": [line_number],
      "side": "RIGHT"
    }
  ]
}
EOF</command>
      </execute_command>
      
      The review will be created with all inline comments attached to specific lines of code.
      
      Note on event types:
      - "COMMENT": Submit general feedback without approval/rejection
      - "REQUEST_CHANGES": Request changes be made before merging
      - "APPROVE": Approve the PR for merging
      
      Example for a review requesting changes:
      <execute_command>
      <command>gh api -X POST repos/RooCodeInc/Roo-Code/pulls/6378/reviews --input - <<EOF
{
  "commit_id": "abc123def4567890...",
  "body": "Thank you for your contribution! I've reviewed the changes and found that the critical issues from the previous review are still pending. I've left some suggestions inline to help improve the implementation.",
  "event": "REQUEST_CHANGES",
  "comments": [
    {
      "path": "packages/cloud/src/CloudService.ts",
      "body": "Missing error handling here...",
      "line": 19,
      "side": "RIGHT"
    },
    {
      "path": "packages/cloud/src/CloudService.ts",
      "body": "Is this intentional? The timeout seems quite high (30s). Could we consider reducing it or making it configurable?",
      "line": 45,
      "side": "RIGHT"
    }
  ]
}
EOF</command>
      </execute_command>
      
      <update_todo_list>
      <todos>
      [x] Fetch pull request information
      [x] Fetch associated issue (if any)
      [x] Fetch pull request diff
      [x] Fetch existing PR comments and reviews
      [x] Check out pull request locally
      [x] Verify existing comments against current code
      [x] Perform comprehensive review
      [x] Prepare review comments
      [x] Preview review with user
      [x] Submit review or create tasks
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="12">
    <name>Create Tasks for Suggestions (Optional)</name>
    <instructions>
      If the user chooses to create individual tasks for each suggestion, use the new_task tool to create separate tasks:
      
      For each numbered finding from your review:
      1. Determine the appropriate mode based on the type of work needed:
         - Use "code" mode for bug fixes, implementation changes, or refactoring
         - Use "translate" mode for internationalization (i18n) issues
         - Use "test" mode for missing or inadequate test coverage
         - Use "docs-extractor" mode for documentation issues
         - Use "architect" mode for structural or design improvements
         - Use "debug" mode for investigating potential bugs
      
      2. Create a clear, actionable task message that includes:
         - The specific issue or suggestion
         - The file(s) and line numbers affected
         - Any relevant context from the PR
         - The expected outcome
      
      3. Use the new_task tool for each suggestion:
      <new_task>
      <mode>[appropriate mode based on task type]</mode>
      <message>Fix [issue type] in [file]: [specific description of what needs to be done]</message>
      </new_task>
      
      Example task creation:
      <new_task>
      <mode>code</mode>
      <message>Fix missing error handling in src/api/users.ts:45-52. The getUserById function should handle cases where the user is not found and return an appropriate error response.</message>
      </new_task>
      
      <new_task>
      <mode>translate</mode>
      <message>Add missing i18n translations for new user profile fields in src/components/UserProfile.tsx. The fields 'bio', 'location', and 'website' need to be wrapped with translation functions.</message>
      </new_task>
      
      After creating all tasks, provide a summary:
      "I've created [X] individual tasks for the review findings:
      - [Y] code fixes/improvements
      - [Z] translation/i18n tasks
      - [etc.]
      
      Each task contains the specific context and requirements for addressing the issue."
    </instructions>
  </step>
</workflow>