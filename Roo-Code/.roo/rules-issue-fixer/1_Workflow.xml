<workflow>
  <step number="1">
    <name>Retrieve Issue Context</name>
    <instructions>
      The user should provide a full GitHub issue URL (e.g., "https://github.com/owner/repo/issues/123") for implementation.
      
      Parse the URL to extract:
      - Owner (organization or username)
      - Repository name
      - Issue number
      
      For example, from https://github.com/RooCodeInc/Roo-Code/issues/123:
      - Owner: RooCodeInc
      - Repo: Roo-Code
      - Issue: 123
      
      Then retrieve the issue:
      
      <execute_command>
      <command>gh issue view [issue-number] --repo [owner]/[repo] --json number,title,body,state,labels,assignees,milestone,createdAt,updatedAt,closedAt,author</command>
      </execute_command>
      
      If the command fails with an authentication error (e.g., "gh: Not authenticated" or "HTTP 401"), ask the user to authenticate:
      <ask_followup_question>
      <question>GitHub CLI is not authenticated. Please run 'gh auth login' in your terminal to authenticate, then let me know when you're ready to continue.</question>
      <follow_up>
      <suggest>I've authenticated, please continue</suggest>
      <suggest>I need help with authentication</suggest>
      <suggest>Let's use a different approach</suggest>
      </follow_up>
      </ask_followup_question>
      
      Analyze the issue to determine:
      1. All requirements and acceptance criteria
      2. Technical details mentioned
      3. Any linked issues or discussions
      
      Note: For PR review feedback, users should use the dedicated pr-fixer mode instead.
    </instructions>
  </step>

  <step number="2">
    <name>Review Issue Comments and Related Context</name>
    <instructions>
      Get all comments on the issue to understand:
      - Additional context or clarifications
      - Maintainer feedback
      - Community suggestions
      - Any decisions or changes to requirements
      
      <execute_command>
      <command>gh issue view [issue number] --repo [owner]/[repo] --comments</command>
      </execute_command>
      
      Also check for:
      1. Related issues mentioned in the body or comments
      2. Linked pull requests
      3. Referenced discussions
      
      If related PRs are mentioned, view them:
      <execute_command>
      <command>gh pr view [pr-number] --repo [owner]/[repo]</command>
      </execute_command>
      
      Document all requirements and constraints found.
    </instructions>
  </step>

  <step number="3">
    <name>Explore Codebase and Related Files</name>
    <instructions>
      Use codebase_search FIRST to understand the codebase structure and find ALL related files:
      
      For Bug Fixes:
      - Search for the broken functionality
      - Find error handling and logging
      - Locate related test files
      - Identify dependencies and imports
      - Find similar patterns in the codebase
      
      For Features:
      - Search for similar existing features
      - Find integration points
      - Locate configuration files
      - Identify patterns to follow
      - Find related components and utilities
      
      Example searches based on issue type:
      - Bug: Search for error messages, function names, component names
      - Feature: Search for similar functionality, API endpoints, UI components
      
      CRITICAL: Always read multiple related files together to understand:
      - Current code patterns and conventions
      - How similar functionality is implemented
      - Testing patterns used in the project
      - Import/export patterns
      - Error handling approaches
      - Configuration and setup patterns
      
      Then use other tools:
      - list_code_definition_names to understand file structure
      - read_file to examine specific implementations (read multiple files at once)
      - search_files for specific patterns or error messages
      
      Also use GitHub CLI to check recent changes:
      <execute_command>
      <command>gh api repos/[owner]/[repo]/commits?path=[file-path]&per_page=10 --jq '.[].sha + " " + .[].commit.message'</command>
      </execute_command>
      
      Search for related PRs:
      <execute_command>
      <command>gh pr list --repo [owner]/[repo] --search "[relevant search terms]" --limit 10</command>
      </execute_command>
      
      Document:
      - All files that need modification
      - Current implementation details and patterns
      - Code conventions to follow (naming, structure, etc.)
      - Test file locations and patterns
      - Related files that might be affected
    </instructions>
  </step>

  <step number="4">
    <name>Create Implementation Plan</name>
    <instructions>
      Based on the issue analysis, create a detailed implementation plan:
      
      For Bug Fixes:
      1. Reproduce the bug locally (if possible)
      2. Identify root cause
      3. Plan the fix approach. The plan should be focused on resolving the issue with a high-quality, targeted fix, while avoiding unrelated changes.
      4. Identify files to modify.
      5. Plan test cases to prevent regression.
      
      For Feature Implementation:
      1. Break down the feature into components
      2. Identify all files that need changes
      3. Plan the implementation approach
      4. Consider edge cases and error handling
      5. Plan test coverage
      
      Present the plan to the user:
      
      <ask_followup_question>
      <question>I've analyzed issue #[number]: "[title]"

      Here's my implementation plan to resolve the issue:

      [Detailed plan with steps and affected files]

      This plan focuses on providing a quality fix for the reported problem without introducing unrelated changes.

      Would you like me to proceed with this implementation?</question>
      <follow_up>
      <suggest>Yes, proceed with the implementation</suggest>
      <suggest>Let me review the issue first</suggest>
      <suggest>Modify the approach for: [specific aspect]</suggest>
      <suggest>Focus only on: [specific part]</suggest>
      </follow_up>
      </ask_followup_question>
    </instructions>
  </step>

  <step number="5">
    <name>Implement the Solution</name>
    <instructions>
      Implement the fix or feature following the plan:
      
      General Guidelines:
      1. Follow existing code patterns and style
      2. Add appropriate error handling
      3. Include necessary comments
      4. Update related documentation
      5. Ensure backward compatibility (if applicable)
      
      For Bug Fixes:
      1. Implement the planned fix, focusing on quality and precision.
      2. The scope of the fix should be as narrow as possible to address the issue. Avoid making changes to code that is not directly related to the fix. This is not an encouragement for one-line hacks, but a guideline to prevent unintended side-effects.
      3. Add regression tests.
      4. Verify the fix resolves the issue.
      5. Check for side effects.
      
      For Features:
      1. Implement incrementally
      2. Test each component as you build
      3. Follow the acceptance criteria exactly
      4. Add comprehensive tests
      5. Update documentation
      
      Use appropriate tools:
      - apply_diff for targeted changes
      - write_to_file for new files
      - search_and_replace for systematic updates
      
      After each significant change, run relevant tests:
      - execute_command to run test suites
      - Check for linting errors
      - Verify functionality works as expected
    </instructions>
  </step>

  <step number="6">
    <name>Verify Acceptance Criteria</name>
    <instructions>
      Systematically verify all acceptance criteria from the issue:
      
      For Bug Fixes:
      1. Confirm the bug no longer reproduces
      2. Follow the exact reproduction steps
      3. Verify expected behavior now occurs
      4. Check no new bugs introduced
      5. Run all related tests
      
      For Features:
      1. Test each acceptance criterion
      2. Verify all Given/When/Then scenarios
      3. Test edge cases
      4. Verify UI changes (if applicable)
      5. Check performance impact
      
      Document verification results:
      - [ ] Criterion 1: [result]
      - [ ] Criterion 2: [result]
      - [ ] All tests passing
      - [ ] No linting errors
      
      If any criteria fail, return to implementation step.
     </instructions>
   </step>
 
   <step number="7">
     <name>Check for Translation Requirements</name>
     <instructions>
       After implementing changes, analyze if any translations are required:
       
       Translation is needed if the implementation includes:
       1. New user-facing text strings in UI components
       2. New error messages or user notifications
       3. Updated documentation files that need localization
       4. New command descriptions or tooltips
       5. Changes to announcement files or release notes
       6. New configuration options with user-visible descriptions
       
       Check for these patterns:
       - Hard-coded strings in React components (.tsx/.jsx files)
       - New entries needed in i18n JSON files
       - Updated markdown documentation files
       - New VSCode command contributions
       - Changes to user-facing configuration schemas
       
       If translations are required:
       
       <new_task>
       <mode>translate</mode>
       <message>Translation needed for issue #[issue-number] implementation.
 
       The following changes require translation into all supported languages:
 
       **Files with new/updated user-facing content:**
       - [List specific files and what content needs translation]
       - [Include context about where the strings appear]
       - [Note any special formatting or constraints]
 
       **Translation scope:**
       - [Specify if it's new strings, updated strings, or both]
       - [List specific JSON keys that need attention]
       - [Note any markdown files that need localization]
 
       **Context for translators:**
       - [Explain the feature/fix being implemented]
       - [Provide context about how the text is used]
       - [Note any technical terms or constraints]
 
       Please ensure all translations maintain consistency with existing terminology and follow the project's localization guidelines.</message>
       </new_task>
       
       Wait for the translation task to complete before proceeding to testing.
       
       If no translations are required, continue to the next step.
     </instructions>
   </step>
 
   <step number="8">
     <name>Run Tests and Checks</name>
    <instructions>
      Run comprehensive tests to ensure quality:
      
      1. Run unit tests for modified files:
         ```
         npm test [test file paths]
         ```
      
      2. Run integration tests if applicable:
         ```
         npm run test:integration
         ```
      
      3. Check for linting errors:
         ```
         npm run lint
         ```
      
      4. Run type checking:
         ```
         npm run type-check
         ```
      
      5. If UI changes, test manually:
         - Build the extension
         - Test in VSCode
         - Verify visual changes
      
      Document all test results and fix any failures.
    </instructions>
  </step>

  <step number="9">
    <name>Prepare Summary</name>
    <instructions>
      Create a comprehensive summary of the implementation:
      
      ## Summary of Changes
      
      **Issue:** #[number] - [title]
      **Type:** [Bug Fix/Feature Implementation]
      
      ### What was done:
      - [List key changes]
      - [Files modified]
      - [Tests added]
      
      ### How it addresses the issue:
      [Explain how each requirement is met]
      
      ### Testing performed:
      - [List all tests run]
      - [Manual testing done]
      - [Verification of acceptance criteria]
      
      ### Files changed:
      - `path/to/file1.ts` - [brief description]
      - `path/to/file2.ts` - [brief description]
      
      ### Potential impacts:
      - [Any breaking changes]
      - [Performance considerations]
      - [Compatibility notes]
      
      Present to user for review:
      
      <ask_followup_question>
      <question>I've completed the implementation for issue #[number]. Here's what was done:

      [Insert summary]

      All acceptance criteria have been met and tests are passing.

      Would you like me to prepare a pull request, or would you like to review the changes first?</question>
      <follow_up>
      <suggest>Create a pull request with these changes</suggest>
      <suggest>Show me the key changes in detail</suggest>
      <suggest>Run additional tests for: [specific area]</suggest>
      <suggest>Make additional changes to: [specific file]</suggest>
      </follow_up>
      </ask_followup_question>
    </instructions>
  </step>

  <step number="10">
    <name>Prepare for Pull Request</name>
    <instructions>
      If user wants to create a pull request, prepare everything needed:
      
      1. Create appropriate branch name:
         - Bug fix: `fix/issue-[number]-[brief-description]`
         - Feature: `feat/issue-[number]-[brief-description]`
      
      2. Ensure all changes are committed:
         ```bash
         git status
         git add [modified files]
         git commit -m "fix: [description] (#[issue-number])"
         ```
      
      3. Push to a fork or branch (depending on user's access):
         ```bash
         git push origin [branch-name]
         ```
      
      4. Prepare comprehensive PR description:
         ```markdown
         ## Description
         
         Fixes #[issue number]
         
         [Detailed description of what was changed and why]
         
         ## Changes Made
         
         - [Specific change 1 with file references]
         - [Specific change 2 with technical details]
         - [Any refactoring or cleanup done]
         
         ## Testing
         
         - [x] All existing tests pass
         - [x] Added tests for [specific functionality]
         - [x] Manual testing completed:
           - [Specific manual test 1]
           - [Specific manual test 2]
         
         ## Verification of Acceptance Criteria
         
         [For each criterion from the issue, show it's met]
         - [x] Criterion 1: [How it's verified]
         - [x] Criterion 2: [How it's verified]
         
         ## Checklist
         
         - [x] Code follows project style guidelines
         - [x] Self-review completed
         - [x] Comments added for complex logic
         - [x] Documentation updated (if needed)
         - [x] No breaking changes (or documented if any)
         - [x] Accessibility checked (for UI changes)
         
         ## Screenshots/Demo (if applicable)
         
         [Add before/after screenshots for UI changes]
         [Add terminal output for CLI changes]
         ```
      
      5. Ask user for final approval:
      
      <ask_followup_question>
      <question>I've prepared everything for the pull request:

      **Branch:** [branch-name]
      **Title:** [PR title]
      **Target:** [owner]/[repo] (main branch)

      Here's the PR description:

      [Show prepared PR description]

      Would you like me to create this pull request to [owner]/[repo]?</question>
      <follow_up>
      <suggest>Yes, create the pull request</suggest>
      <suggest>Let me review the PR description first</suggest>
      <suggest>Change the PR title to: [let me specify]</suggest>
      <suggest>Add more details about: [specific aspect]</suggest>
      </follow_up>
      </ask_followup_question>
    </instructions>
  </step>

  <step number="11">
    <name>Create Pull Request</name>
    <instructions>
      Once user approves, create the pull request using GitHub CLI:
      
      If the user doesn't have push access to [owner]/[repo], fork the repository:
      <execute_command>
      <command>gh repo fork [owner]/[repo] --clone</command>
      </execute_command>
      
      Create the pull request:
      <execute_command>
      <command>gh pr create --repo [owner]/[repo] --base main --title "[Type]: [Brief description] (#[issue-number])" --body "[Complete PR description from step 10]" --maintainer-can-modify</command>
      </execute_command>
      
      The gh CLI will automatically handle the fork workflow if needed.
      
      After PR creation:
      1. Capture the PR number and URL from the command output
      2. Link the PR to the issue by commenting on the issue
      3. Inform the user of the successful creation
      
      <execute_command>
      <command>gh issue comment [original issue number] --repo [owner]/[repo] --body "PR #[new PR number] has been created to address this issue"</command>
      </execute_command>
      
      Final message to user:
      ```
      ✅ Pull Request Created Successfully!
      
      PR #[number]: [title]
      URL: [PR URL]
      
      The PR has been created and linked to issue #[issue number].
      
      Next steps:
      1. The PR will be reviewed by maintainers
      2. Address any feedback in the PR comments
      3. Once approved, it will be merged
      
      You can track the PR status at: [PR URL]
      ```
    </instructions>
  </step>

  <step number="12">
    <name>Monitor PR Checks</name>
    <instructions>
      After the PR is created, monitor the CI/CD checks to ensure they pass:
      
      <execute_command>
      <command>gh pr checks [PR number] --repo [owner]/[repo] --watch</command>
      </execute_command>
      
      This command will:
      1. Display all CI/CD checks configured for the repository
      2. Show the status of each check in real-time
      3. Update automatically as checks complete
      4. Exit when all checks have finished running
      
      Monitor the output and note:
      - Which checks are running (e.g., tests, linting, build)
      - Any checks that fail and their error messages
      - The overall status of the PR checks
      
      If any checks fail:
      1. Analyze the failure logs
      2. Identify what needs to be fixed
      3. Ask the user if they want you to address the failures
      
      <ask_followup_question>
      <question>The PR checks have completed. Here's the status:

      [Show check results - passing/failing]

      [If all pass]: All checks have passed successfully! The PR is ready for review.

      [If any fail]: Some checks have failed:
      - [Failed check 1]: [Brief error description]
      - [Failed check 2]: [Brief error description]

      Would you like me to fix these issues?</question>
      <follow_up>
      <suggest>Yes, please fix the failing checks</suggest>
      <suggest>Show me the detailed error logs</suggest>
      <suggest>I'll handle the failures manually</suggest>
      <suggest>The PR is fine as-is, these failures are expected</suggest>
      </follow_up>
      </ask_followup_question>
      
      If user wants fixes:
      1. Create a plan to address each failure
      2. Make necessary code changes
      3. Commit and push the fixes
      4. Monitor checks again to ensure they pass
      
      Important notes:
      - The --watch flag will keep the command running until all checks complete
      - This step helps ensure the PR meets all quality standards before review
      - Early detection of CI/CD failures saves reviewer time
    </instructions>
  </step>
</workflow>