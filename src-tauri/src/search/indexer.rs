use anyhow::{Context, Result};
use log::{debug, error, info};
use rayon::prelude::*;
use rusqlite::{params, Connection};
// use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex};
use tokio::sync::mpsc;
use walkdir::{DirEntry, WalkDir};

use crate::commands::search::{CodeChunk, IndexingProgress};
use super::analyzer::ClaudeAnalyzer;

/// Configuration for the indexing engine
#[derive(Debug, Clone)]
pub struct IndexingConfig {
    pub max_file_size: u64,
    pub chunk_size: usize,
    pub chunk_overlap: usize,
    pub exclude_patterns: Vec<String>,
    pub include_patterns: Vec<String>,
    pub languages: Vec<String>,
}

impl Default for IndexingConfig {
    fn default() -> Self {
        Self {
            max_file_size: 10 * 1024 * 1024, // 10MB
            chunk_size: 100, // lines per chunk
            chunk_overlap: 20, // overlapping lines between chunks
            exclude_patterns: vec![
                "node_modules".to_string(),
                "target".to_string(),
                "dist".to_string(),
                "build".to_string(),
                ".git".to_string(),
                "vendor".to_string(),
            ],
            include_patterns: vec![],
            languages: vec![
                "rs".to_string(), "js".to_string(), "ts".to_string(), "tsx".to_string(),
                "jsx".to_string(), "py".to_string(), "go".to_string(), "java".to_string(),
                "cpp".to_string(), "c".to_string(), "h".to_string(), "hpp".to_string(),
                "cs".to_string(), "rb".to_string(), "php".to_string(), "swift".to_string(),
                "kt".to_string(), "scala".to_string(), "r".to_string(), "m".to_string(),
                "sql".to_string(), "sh".to_string(), "yaml".to_string(), "yml".to_string(),
                "json".to_string(), "xml".to_string(), "toml".to_string(), "md".to_string(),
            ],
        }
    }
}

/// Main indexing engine for processing files
pub struct IndexingEngine {
    db: Arc<Mutex<Connection>>,
    claude_analyzer: Arc<ClaudeAnalyzer>,
    config: IndexingConfig,
    progress_tx: Option<mpsc::Sender<IndexingProgress>>,
}

impl IndexingEngine {
    pub fn new(
        db: Arc<Mutex<Connection>>,
        claude_analyzer: Arc<ClaudeAnalyzer>,
        config: IndexingConfig,
    ) -> Self {
        Self {
            db,
            claude_analyzer,
            config,
            progress_tx: None,
        }
    }

    pub fn set_progress_channel(&mut self, tx: mpsc::Sender<IndexingProgress>) {
        self.progress_tx = Some(tx);
    }

    /// Index an entire project
    pub async fn index_project(&self, project_path: &Path, project_id: &str) -> Result<()> {
        info!("Starting indexing for project: {} at {:?}", project_id, project_path);
        
        // Discover all files to index
        let files = self.discover_files(project_path)?;
        let total_files = files.len() as u32;
        
        info!("Found {} files to index", total_files);
        
        // Send initial progress
        self.send_progress(IndexingProgress {
            total_files,
            processed_files: 0,
            current_file: None,
            errors: vec![],
            status: "indexing".to_string(),
            session_id: None,
            phase: "discovery".to_string(),
        }).await;
        
        // Process files in parallel batches
        let batch_size = 10;
        let mut processed = 0u32;
        let mut errors = Vec::new();
        
        for batch in files.chunks(batch_size) {
            let batch_results: Vec<Result<()>> = batch
                .par_iter()
                .map(|file_path| {
                    self.process_file(file_path, project_id, project_path)
                })
                .collect();
            
            // Collect errors
            for result in batch_results {
                if let Err(e) = result {
                    error!("Error processing file: {}", e);
                    errors.push(e.to_string());
                }
                processed += 1;
                
                // Send progress update
                let progress = IndexingProgress {
                    total_files,
                    processed_files: processed,
                    current_file: batch.last().map(|p| p.display().to_string()),
                    errors: errors.clone(),
                    status: "indexing".to_string(),
                    session_id: None,
                    phase: "analysis".to_string(),
                };
                
                // Use blocking send since we're not in async context here
                if let Some(tx) = &self.progress_tx {
                    let _ = tx.blocking_send(progress);
                }
            }
        }
        
        // Send completion progress
        self.send_progress(IndexingProgress {
            total_files,
            processed_files: total_files,
            current_file: None,
            errors,
            status: "completed".to_string(),
            session_id: None,
            phase: "completed".to_string(),
        }).await;
        
        info!("Indexing completed for project: {}", project_id);
        Ok(())
    }

    /// Discover all files to be indexed
    fn discover_files(&self, project_path: &Path) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        
        // Check for .gitignore
        let gitignore_path = project_path.join(".gitignore");
        let gitignore_patterns = if gitignore_path.exists() {
            self.parse_gitignore(&gitignore_path)?
        } else {
            vec![]
        };
        
        for entry in WalkDir::new(project_path)
            .follow_links(false)
            .into_iter()
            .filter_entry(|e| self.should_index_entry(e, &gitignore_patterns))
        {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() && self.is_supported_file(path)? {
                files.push(path.to_path_buf());
            }
        }
        
        Ok(files)
    }

    /// Check if a directory entry should be indexed
    fn should_index_entry(&self, entry: &DirEntry, gitignore_patterns: &[String]) -> bool {
        let path = entry.path();
        let name = entry.file_name().to_string_lossy();
        
        // Check exclude patterns
        for pattern in &self.config.exclude_patterns {
            if name.contains(pattern) || path.to_string_lossy().contains(pattern) {
                return false;
            }
        }
        
        // Check gitignore patterns
        for pattern in gitignore_patterns {
            if name == pattern.as_str() || path.to_string_lossy().contains(pattern) {
                return false;
            }
        }
        
        true
    }

    /// Check if a file is supported for indexing
    fn is_supported_file(&self, path: &Path) -> Result<bool> {
        // Check file size
        let metadata = fs::metadata(path)?;
        if metadata.len() > self.config.max_file_size {
            return Ok(false);
        }
        
        // Check file extension
        if let Some(ext) = path.extension() {
            let ext_str = ext.to_string_lossy().to_lowercase();
            Ok(self.config.languages.contains(&ext_str))
        } else {
            Ok(false)
        }
    }

    /// Process a single file
    fn process_file(&self, file_path: &Path, project_id: &str, project_root: &Path) -> Result<()> {
        debug!("Processing file: {:?}", file_path);
        
        // Read file content
        let content = fs::read_to_string(file_path)
            .with_context(|| format!("Failed to read file: {:?}", file_path))?;
        
        // Calculate file hash
        let file_hash = self.calculate_file_hash(&content);
        
        // Get relative path
        let relative_path = file_path
            .strip_prefix(project_root)
            .unwrap_or(file_path)
            .to_string_lossy()
            .to_string();
        
        // Check if file needs re-indexing
        if !self.needs_reindexing(project_id, &relative_path, &file_hash)? {
            debug!("File hasn't changed, skipping: {}", relative_path);
            return Ok(());
        }
        
        // Detect language
        let language = self.detect_language(file_path);
        
        // Create chunks
        let mut chunks = self.create_chunks(&content, &relative_path, &language);
        
        // Store file metadata
        self.store_file_metadata(project_id, &relative_path, &content, &file_hash, &language)?;
        
        // Analyze chunks with Claude (in batches for efficiency)
        self.analyze_chunks_with_claude(&mut chunks, &language)?;
        
        // Store chunks with Claude analysis
        self.store_chunks(project_id, chunks)?;
        
        Ok(())
    }

    /// Calculate SHA256 hash of file content
    fn calculate_file_hash(&self, content: &str) -> String {
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        format!("{:x}", hasher.finalize())
    }

    /// Check if a file needs re-indexing
    fn needs_reindexing(&self, project_id: &str, file_path: &str, file_hash: &str) -> Result<bool> {
        let conn = self.db.lock().unwrap();
        
        let exists: bool = conn
            .query_row(
                "SELECT file_hash != ?3 OR NOT is_indexed 
                 FROM file_metadata 
                 WHERE project_id = ?1 AND file_path = ?2",
                params![project_id, file_path, file_hash],
                |row| row.get(0),
            )
            .unwrap_or(true); // If file not found, needs indexing
        
        Ok(exists)
    }

    /// Detect programming language from file extension
    fn detect_language(&self, path: &Path) -> String {
        path.extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| {
                match ext.to_lowercase().as_str() {
                    "rs" => "rust",
                    "js" | "mjs" => "javascript",
                    "ts" | "tsx" => "typescript",
                    "jsx" => "javascriptreact",
                    "py" => "python",
                    "go" => "go",
                    "java" => "java",
                    "cpp" | "cc" | "cxx" => "cpp",
                    "c" => "c",
                    "h" | "hpp" => "cpp",
                    "cs" => "csharp",
                    "rb" => "ruby",
                    "php" => "php",
                    "swift" => "swift",
                    "kt" | "kts" => "kotlin",
                    "scala" => "scala",
                    "r" => "r",
                    "m" => "objective-c",
                    "sql" => "sql",
                    "sh" | "bash" => "shellscript",
                    "yaml" | "yml" => "yaml",
                    "json" => "json",
                    "xml" => "xml",
                    "toml" => "toml",
                    "md" => "markdown",
                    _ => ext,
                }
            })
            .unwrap_or("text")
            .to_string()
    }

    /// Create chunks from file content
    fn create_chunks(&self, content: &str, file_path: &str, language: &str) -> Vec<CodeChunk> {
        let lines: Vec<&str> = content.lines().collect();
        let mut chunks = Vec::new();
        let chunk_size = self.config.chunk_size;
        let overlap = self.config.chunk_overlap;
        
        if lines.is_empty() {
            return chunks;
        }
        
        let mut chunk_id = 0;
        let mut start = 0;
        
        while start < lines.len() {
            let end = (start + chunk_size).min(lines.len());
            let chunk_lines = &lines[start..end];
            let chunk_content = chunk_lines.join("\n");
            
            chunks.push(CodeChunk {
                id: None,
                project_id: String::new(), // Will be set when storing
                file_path: file_path.to_string(),
                chunk_id,
                start_line: (start + 1) as u32, // 1-based line numbers
                end_line: end as u32,
                content: chunk_content,
                language: language.to_string(),
                claude_summary: None,
                semantic_tags: None,
                last_analyzed: None,
            });
            
            chunk_id += 1;
            
            // Move to next chunk with overlap
            if end >= lines.len() {
                break;
            }
            start = end - overlap;
        }
        
        chunks
    }

    /// Store file metadata in database
    fn store_file_metadata(
        &self,
        project_id: &str,
        file_path: &str,
        content: &str,
        file_hash: &str,
        language: &str,
    ) -> Result<()> {
        let conn = self.db.lock().unwrap();
        
        let file_size = content.len() as i64;
        let last_modified = chrono::Utc::now().to_rfc3339();
        
        conn.execute(
            "INSERT INTO file_metadata 
             (project_id, file_path, file_size, last_modified, file_hash, language, is_indexed) 
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, 0)
             ON CONFLICT(project_id, file_path) DO UPDATE SET
                file_size = ?3,
                last_modified = ?4,
                file_hash = ?5,
                language = ?6,
                is_indexed = 0,
                updated_at = CURRENT_TIMESTAMP",
            params![project_id, file_path, file_size, last_modified, file_hash, language],
        )?;
        
        Ok(())
    }

    /// Store code chunks in database
    fn store_chunks(&self, project_id: &str, chunks: Vec<CodeChunk>) -> Result<()> {
        let conn = self.db.lock().unwrap();
        
        // Delete existing chunks for this file
        if let Some(first_chunk) = chunks.first() {
            conn.execute(
                "DELETE FROM code_index WHERE project_id = ?1 AND file_path = ?2",
                params![project_id, &first_chunk.file_path],
            )?;
        }
        
        // Insert new chunks with Claude analysis
        for chunk in chunks {
            conn.execute(
                "INSERT INTO code_index 
                 (project_id, file_path, chunk_id, start_line, end_line, content, language, 
                  claude_summary, semantic_tags, last_analyzed) 
                 VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
                params![
                    project_id,
                    chunk.file_path,
                    chunk.chunk_id,
                    chunk.start_line,
                    chunk.end_line,
                    chunk.content,
                    chunk.language,
                    chunk.claude_summary,
                    chunk.semantic_tags,
                    chunk.last_analyzed,
                ],
            )?;
        }
        
        Ok(())
    }

    /// Parse .gitignore file
    fn parse_gitignore(&self, gitignore_path: &Path) -> Result<Vec<String>> {
        let content = fs::read_to_string(gitignore_path)?;
        let patterns: Vec<String> = content
            .lines()
            .filter(|line| !line.trim().is_empty() && !line.trim().starts_with('#'))
            .map(|line| line.trim().to_string())
            .collect();
        Ok(patterns)
    }

    /// Send progress update
    async fn send_progress(&self, progress: IndexingProgress) {
        if let Some(tx) = &self.progress_tx {
            let _ = tx.send(progress).await;
        }
    }
    
    /// Analyze chunks with Claude for semantic understanding
    fn analyze_chunks_with_claude(&self, chunks: &mut Vec<CodeChunk>, language: &str) -> Result<()> {
        use tokio::runtime::Runtime;
        
        // Create a runtime for async operations
        let rt = Runtime::new()?;
        
        // Process chunks in batches of 5 for efficiency
        const BATCH_SIZE: usize = 5;
        
        for batch in chunks.chunks_mut(BATCH_SIZE) {
            // Prepare batch for analysis
            let batch_content: Vec<(&str, &str)> = batch
                .iter()
                .map(|chunk| (chunk.content.as_str(), language))
                .collect();
            
            // Analyze batch with Claude
            let analysis_results = rt.block_on(async {
                self.claude_analyzer.batch_analyze_code(batch_content).await
            })?;
            
            // Apply analysis results to chunks
            for (chunk, analysis) in batch.iter_mut().zip(analysis_results.iter()) {
                chunk.claude_summary = Some(analysis.summary.clone());
                chunk.semantic_tags = Some(serde_json::to_string(&analysis.concepts)?);
                chunk.last_analyzed = Some(chrono::Utc::now().to_rfc3339());
            }
        }
        
        Ok(())
    }
}