import { 
  ActionUsageData, 
  FollowUpQuestion, 
  QuestionContext,
  QuestionCategory 
} from './followUpQuestions';
import { invoke } from '@tauri-apps/api/core';

/**
 * Analytics system for tracking follow-up question usage and learning user preferences
 */
export class FollowUpAnalytics {
  private static instance: FollowUpAnalytics;
  private sessionId: string;
  private usageHistory: ActionUsageData[] = [];
  private categoryPreferences: Map<QuestionCategory, number> = new Map();
  private actionSuccessRates: Map<string, { selected: number; total: number }> = new Map();

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.loadAnalytics();
  }

  static getInstance(): FollowUpAnalytics {
    if (!FollowUpAnalytics.instance) {
      FollowUpAnalytics.instance = new FollowUpAnalytics();
    }
    return FollowUpAnalytics.instance;
  }

  /**
   * Track when a question is shown to the user
   */
  trackQuestionShown(question: FollowUpQuestion, _context: QuestionContext) {
    const actionData = this.actionSuccessRates.get(question.id) || { selected: 0, total: 0 };
    actionData.total++;
    this.actionSuccessRates.set(question.id, actionData);
  }

  /**
   * Track when a user selects a question
   */
  trackQuestionSelected(question: FollowUpQuestion, context: QuestionContext) {
    const usageData: ActionUsageData = {
      actionId: question.id,
      context,
      selected: true,
      timestamp: new Date(),
      sessionId: this.sessionId,
      projectType: this.detectProjectType(context)
    };

    this.usageHistory.push(usageData);
    
    // Update success rate
    const actionData = this.actionSuccessRates.get(question.id) || { selected: 0, total: 0 };
    actionData.selected++;
    this.actionSuccessRates.set(question.id, actionData);

    // Update category preferences
    const currentPref = this.categoryPreferences.get(question.category) || 0;
    this.categoryPreferences.set(question.category, currentPref + 1);

    // Save analytics data
    this.saveAnalytics();
  }

  /**
   * Track when a user dismisses questions
   */
  trackQuestionsDismissed(questions: FollowUpQuestion[], context: QuestionContext) {
    questions.forEach(question => {
      const usageData: ActionUsageData = {
        actionId: question.id,
        context,
        selected: false,
        timestamp: new Date(),
        sessionId: this.sessionId
      };
      this.usageHistory.push(usageData);
    });
  }

  /**
   * Get confidence boost for a question based on historical usage
   */
  getConfidenceBoost(questionId: string, category: QuestionCategory): number {
    let boost = 0;

    // Boost based on success rate
    const actionData = this.actionSuccessRates.get(questionId);
    if (actionData && actionData.total > 0) {
      const successRate = actionData.selected / actionData.total;
      boost += successRate * 0.2; // Up to 0.2 boost
    }

    // Boost based on category preference
    const categoryPref = this.categoryPreferences.get(category) || 0;
    const totalPrefs = Array.from(this.categoryPreferences.values()).reduce((a, b) => a + b, 0);
    if (totalPrefs > 0) {
      const categoryRate = categoryPref / totalPrefs;
      boost += categoryRate * 0.1; // Up to 0.1 boost
    }

    return Math.min(boost, 0.3); // Cap at 0.3 total boost
  }

  /**
   * Get personalized question rankings based on usage history
   */
  getPersonalizedRankings(questions: FollowUpQuestion[]): FollowUpQuestion[] {
    return questions.map(question => ({
      ...question,
      confidence: Math.min(
        question.confidence + this.getConfidenceBoost(question.id, question.category),
        1.0
      )
    })).sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Analyze usage patterns to identify user preferences
   */
  analyzeUserPatterns(): {
    preferredCategories: QuestionCategory[];
    frequentActions: string[];
    sessionInsights: {
      totalSuggestions: number;
      selectionRate: number;
      averageTimeToSelect: number;
    };
  } {
    // Get preferred categories
    const preferredCategories = Array.from(this.categoryPreferences.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([category]) => category);

    // Get frequent actions
    const frequentActions = Array.from(this.actionSuccessRates.entries())
      .filter(([, data]) => data.selected > 2)
      .sort((a, b) => b[1].selected - a[1].selected)
      .slice(0, 5)
      .map(([actionId]) => actionId);

    // Calculate session insights
    const totalSuggestions = Array.from(this.actionSuccessRates.values())
      .reduce((sum, data) => sum + data.total, 0);
    const totalSelections = Array.from(this.actionSuccessRates.values())
      .reduce((sum, data) => sum + data.selected, 0);
    const selectionRate = totalSuggestions > 0 ? totalSelections / totalSuggestions : 0;

    return {
      preferredCategories,
      frequentActions,
      sessionInsights: {
        totalSuggestions,
        selectionRate,
        averageTimeToSelect: 0 // TODO: Implement time tracking
      }
    };
  }

  /**
   * Detect project type based on context
   */
  private detectProjectType(context: QuestionContext): string | undefined {
    const files = context.currentFiles;
    
    // Check if files is defined and is an array
    if (!files || !Array.isArray(files) || files.length === 0) {
      return undefined;
    }
    
    if (files.some(f => f.includes('package.json'))) {
      if (files.some(f => f.includes('react') || f.endsWith('.tsx'))) return 'react';
      if (files.some(f => f.includes('vue') || f.endsWith('.vue'))) return 'vue';
      return 'node';
    }
    
    if (files.some(f => f.includes('Cargo.toml'))) return 'rust';
    if (files.some(f => f.includes('pyproject.toml') || f.includes('requirements.txt'))) return 'python';
    if (files.some(f => f.includes('go.mod'))) return 'go';
    
    return undefined;
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Load analytics from storage
   */
  private async loadAnalytics() {
    try {
      const data = await invoke<string>('get_analytics_data');
      if (data) {
        const parsed = JSON.parse(data);
        this.usageHistory = parsed.usageHistory || [];
        this.categoryPreferences = new Map(Object.entries(parsed.categoryPreferences || {}) as [QuestionCategory, number][]);
        this.actionSuccessRates = new Map(Object.entries(parsed.actionSuccessRates || {}));
      }
    } catch (error) {
      console.debug('No analytics data found or error loading:', error);
    }
  }

  /**
   * Save analytics to storage
   */
  private async saveAnalytics() {
    try {
      const data = {
        usageHistory: this.usageHistory.slice(-1000), // Keep last 1000 entries
        categoryPreferences: Object.fromEntries(this.categoryPreferences),
        actionSuccessRates: Object.fromEntries(this.actionSuccessRates)
      };
      await invoke('save_analytics_data', { data: JSON.stringify(data) });
    } catch (error) {
      console.error('Error saving analytics:', error);
    }
  }

  /**
   * Export analytics data for analysis
   */
  exportAnalytics(): string {
    const patterns = this.analyzeUserPatterns();
    const data = {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      patterns,
      usageHistory: this.usageHistory,
      categoryPreferences: Object.fromEntries(this.categoryPreferences),
      actionSuccessRates: Object.fromEntries(this.actionSuccessRates)
    };
    return JSON.stringify(data, null, 2);
  }

  /**
   * Reset analytics data
   */
  reset() {
    this.usageHistory = [];
    this.categoryPreferences.clear();
    this.actionSuccessRates.clear();
    this.sessionId = this.generateSessionId();
    this.saveAnalytics();
  }
}